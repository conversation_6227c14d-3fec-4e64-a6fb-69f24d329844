pipeline {
    agent {
        node {
            label ''
        }
    }
    stages {
        stage('初始化日期参数') {
            steps {
                script {
                    // 获取当前日期
                    def now = new Date()
                    def calendar = Calendar.getInstance()
                    calendar.setTime(now)

                    // 计算上个月的第一天
                    calendar.add(Calendar.MONTH, -1)
                    calendar.set(Calendar.DAY_OF_MONTH, 1)
                    calendar.set(Calendar.HOUR_OF_DAY, 0)
                    calendar.set(Calendar.MINUTE, 0)
                    calendar.set(Calendar.SECOND, 0)
                    calendar.set(Calendar.MILLISECOND, 0)
                    def lastMonthFirst = calendar.getTime()

                    // 计算上个月的最后一天
                    calendar.add(Calendar.MONTH, 1)
                    calendar.add(Calendar.DAY_OF_MONTH, -1)
                    calendar.set(Calendar.HOUR_OF_DAY, 23)
                    calendar.set(Calendar.MINUTE, 59)
                    calendar.set(Calendar.SECOND, 59)
                    def lastMonthLast = calendar.getTime()

                    // 计算这个月的第一天
                    calendar.add(Calendar.DAY_OF_MONTH, 1)
                    calendar.set(Calendar.HOUR_OF_DAY, 0)
                    calendar.set(Calendar.MINUTE, 0)
                    calendar.set(Calendar.SECOND, 0)
                    def thisMonthFirst = calendar.getTime()

                    // 格式化日期
                    def dateFormat = new java.text.SimpleDateFormat('yyyyMMdd')
                    def datetimeFormat = new java.text.SimpleDateFormat('yyyy-MM-dd HH:mm:ss')

                    // 将日期变量设置为环境变量，供后续步骤使用
                    env.START_DATE = dateFormat.format(lastMonthFirst)
                    env.END_DATE = dateFormat.format(lastMonthLast)
                    env.START_DATETIME = datetimeFormat.format(lastMonthFirst)
                    env.END_DATETIME = datetimeFormat.format(lastMonthLast)
                    env.SERVICE_END_DATETIME = datetimeFormat.format(thisMonthFirst)

                    // 打印生成的日期参数
                    echo "自动生成的日期参数:"
                    echo "START_DATE: ${env.START_DATE}"
                    echo "END_DATE: ${env.END_DATE}"
                    echo "START_DATETIME: ${env.START_DATETIME}"
                    echo "END_DATETIME: ${env.END_DATETIME}"
                    echo "SERVICE_END_DATETIME: ${env.SERVICE_END_DATETIME}"
                }
            }
        }

        stage('更新配置文件') {
            steps {
                script {
                    withEnv(['JENKINS_NODE_COOKIE=background_job']) {
                        sh label: '更新seg_config.json配置', script: """
                            cd /data/report
                            # 备份原配置文件
                            cp seg_config.json seg_config.json.bak

                            # 使用sed替换配置文件中的日期参数
                            sed -i 's/"start_date": "[^"]*"/"start_date": "${env.START_DATE}"/g' seg_config.json
                            sed -i 's/"end_date": "[^"]*"/"end_date": "${env.END_DATE}"/g' seg_config.json
                            sed -i 's/"start_datetime": "[^"]*"/"start_datetime": "${env.START_DATETIME}"/g' seg_config.json
                            sed -i 's/"end_datetime": "[^"]*"/"end_datetime": "${env.END_DATETIME}"/g' seg_config.json
                            sed -i 's/"service_end_datetime": "[^"]*"/"service_end_datetime": "${env.SERVICE_END_DATETIME}"/g' seg_config.json

                            # 显示更新后的配置
                            echo "更新后的seg_config.json配置:"
                            cat seg_config.json
                        """
                    }
                }
            }
        }

        stage('获取全省通报指标数据') {
            steps {
                script {
                    withEnv(['JENKINS_NODE_COOKIE=background_job']) {
                        sh label: '执行数据分析', script: "cd /data/report && /data/report/analyzeTransmissionIndicatorTrendsStep1_x86 range ${env.START_DATE} ${env.END_DATE}"
                    }
                }
            }
        }

        stage('生成指定路段通报指标报告') {
            steps {
                script {
                    withEnv(['JENKINS_NODE_COOKIE=background_job']) {
                        sh label: '生成报告', script: "cd /data/report && /data/report/awesomeProject9"
                    }
                }
            }
        }
    }
}
