pipeline {
    agent {
        node {
            label ''
        }
    }
    stages {
        stage('获取全省通报指标数据') {
            steps {
                script {
                    // 获取当前日期
                    def now = new Date()
                    def calendar = Calendar.getInstance()
                    calendar.setTime(now)
                    
                    // 计算上个月的第一天
                    calendar.add(Calendar.MONTH, -1)
                    calendar.set(Calendar.DAY_OF_MONTH, 1)
                    calendar.set(Calendar.HOUR_OF_DAY, 0)
                    calendar.set(Calendar.MINUTE, 0)
                    calendar.set(Calendar.SECOND, 0)
                    calendar.set(Calendar.MILLISECOND, 0)
                    def lastMonthFirst = calendar.getTime()
                    
                    // 计算上个月的最后一天
                    calendar.add(Calendar.MONTH, 1)
                    calendar.add(Calendar.DAY_OF_MONTH, -1)
                    calendar.set(Calendar.HOUR_OF_DAY, 23)
                    calendar.set(Calendar.MINUTE, 59)
                    calendar.set(Calendar.SECOND, 59)
                    def lastMonthLast = calendar.getTime()
                    
                    // 计算这个月的第一天
                    calendar.add(Calendar.DAY_OF_MONTH, 1)
                    calendar.set(Calendar.HOUR_OF_DAY, 0)
                    calendar.set(Calendar.MINUTE, 0)
                    calendar.set(Calendar.SECOND, 0)
                    def thisMonthFirst = calendar.getTime()
                    
                    // 格式化日期
                    def dateFormat = new java.text.SimpleDateFormat('yyyyMMdd')
                    def datetimeFormat = new java.text.SimpleDateFormat('yyyy-MM-dd HH:mm:ss')
                    
                    def START_DATE = dateFormat.format(lastMonthFirst)
                    def END_DATE = dateFormat.format(lastMonthLast)
                    def START_DATETIME = datetimeFormat.format(lastMonthFirst)
                    def END_DATETIME = datetimeFormat.format(lastMonthLast)
                    def SERVICE_END_DATETIME = datetimeFormat.format(thisMonthFirst)
                    
                    // 打印生成的日期参数
                    echo "自动生成的日期参数:"
                    echo "START_DATE: ${START_DATE}"
                    echo "END_DATE: ${END_DATE}"
                    echo "START_DATETIME: ${START_DATETIME}"
                    echo "END_DATETIME: ${END_DATETIME}"
                    echo "SERVICE_END_DATETIME: ${SERVICE_END_DATETIME}"
                    
                    withEnv(['JENKINS_NODE_COOKIE=background_job']) {
                        sh label: '', script: "echo ${START_DATE} ${END_DATE} ${START_DATETIME} ${END_DATETIME} ${SERVICE_END_DATETIME}"
                    }
                }
            }
        }
    }
}
